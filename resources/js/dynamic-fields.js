/**
 * Dynamic Fields functionality
 */

'use strict';

$(document).ready(function() {

  // Field type configurations
  const fieldTypeConfigs = {
    text: {
      inputType: 'text',
      hasOptions: false,
      defaultValidation: ['string', 'max:255']
    },
    number: {
      inputType: 'number',
      hasOptions: false,
      defaultValidation: ['numeric']
    },
    date: {
      inputType: 'date',
      hasOptions: false,
      defaultValidation: ['date']
    },
    select: {
      inputType: 'select',
      hasOptions: true,
      defaultValidation: ['string']
    },
    textarea: {
      inputType: 'textarea',
      hasOptions: false,
      defaultValidation: ['string', 'max:1000']
    },
    file: {
      inputType: 'file',
      hasOptions: false,
      defaultValidation: ['file']
    },
    checkbox: {
      inputType: 'checkbox',
      hasOptions: true,
      defaultValidation: ['array']
    },
    radio: {
      inputType: 'radio',
      hasOptions: true,
      defaultValidation: ['string']
    }
  };

  // Initialize dynamic field functionality
  function initializeDynamicFields() {
    try {
      console.log('Initializing dynamic fields...');

      // Add event listeners for field type changes
      $(document).on('change', '[name="type"]', function() {
        console.log('Field type changed:', $(this).val());
        handleFieldTypeChange(this);
      });

      // Add event listeners for adding/removing options
      $(document).on('click', '.add-option-btn', function(e) {
        e.preventDefault();
        console.log('Add option button clicked');
        addFieldOption(this);
      });

      $(document).on('click', '.remove-option-btn', function(e) {
        e.preventDefault();
        console.log('Remove option button clicked');
        removeFieldOption(this);
      });

      // Initialize existing field forms
      $('[name="type"]').each(function() {
        handleFieldTypeChange(this);
      });

      console.log('Dynamic fields initialized successfully');
    } catch (error) {
      console.error('Error initializing dynamic fields:', error);
      showToast('error', 'Có lỗi xảy ra khi khởi tạo dynamic fields');
    }
  }

  function handleFieldTypeChange(selectElement) {
    try {
      const $selectElement = $(selectElement);
      let fieldType = $selectElement.val();
      const $formContainer = $selectElement.closest('.field-form-container');

      // Debug logging
      console.log('handleFieldTypeChange called with fieldType:', fieldType);
      console.log('Available fieldTypeConfigs keys:', Object.keys(fieldTypeConfigs));

      if ($formContainer.length === 0) {
        console.warn('Form container not found for field type change');
        return;
      }

      // Normalize field type (trim whitespace, convert to lowercase)
      if (fieldType) {
        fieldType = fieldType.toString().trim().toLowerCase();
      }

      if (!fieldType) {
        console.warn('Field type is empty or null');
        return;
      }

      // Check if field type exists in config
      let config = fieldTypeConfigs[fieldType];

      if (!config) {
        console.warn('Field type config not found for:', fieldType);
        console.log('Attempting to find similar field type...');

        // Try to find a similar field type (case insensitive)
        const availableTypes = Object.keys(fieldTypeConfigs);
        const matchedType = availableTypes.find(type =>
          type.toLowerCase() === fieldType.toLowerCase()
        );

        if (matchedType) {
          console.log('Found matching field type:', matchedType);
          config = fieldTypeConfigs[matchedType];
          fieldType = matchedType; // Use the correct case
        } else {
          // Create a default config for unknown field types
          console.warn('Creating default config for unknown field type:', fieldType);
          config = {
            inputType: 'text',
            hasOptions: false,
            defaultValidation: ['string']
          };
          showToast('warning', `Field type "${fieldType}" không được hỗ trợ, sử dụng cấu hình mặc định`);
        }
      }

      console.log('Using config for field type:', fieldType, config);

      // Show/hide options section
      const $optionsSection = $formContainer.find('.field-options-section');
      if ($optionsSection.length) {
        if (config.hasOptions) {
          $optionsSection.show();
          console.log('Showing options section for field type:', fieldType);
        } else {
          $optionsSection.hide();
          // Clear options when hiding
          $optionsSection.find('.options-container').empty();
          console.log('Hiding options section for field type:', fieldType);
        }
      }

      // Update validation rules suggestions
      updateValidationSuggestions($formContainer, config.defaultValidation);

      // Update placeholder suggestions
      updatePlaceholderSuggestions($formContainer, fieldType);

      console.log('Field type change handled successfully');

    } catch (error) {
      console.error('Error handling field type change:', error);
      showToast('error', 'Có lỗi xảy ra khi thay đổi loại field: ' + error.message);
    }
  }

  function updateValidationSuggestions($container, defaultRules) {
    try {
      const $validationInput = $container.find('[name="validation_rules"]');
      if ($validationInput.length && !$validationInput.val()) {
        $validationInput.attr('placeholder', defaultRules.join('|'));
      }
    } catch (error) {
      console.error('Error updating validation suggestions:', error);
    }
  }

  function updatePlaceholderSuggestions($container, fieldType) {
    try {
      const $placeholderInput = $container.find('[name="placeholder"]');
      if ($placeholderInput.length === 0) return;

      const suggestions = {
        text: 'Nhập văn bản...',
        number: 'Nhập số...',
        date: 'Chọn ngày...',
        select: 'Chọn một tùy chọn...',
        textarea: 'Nhập mô tả...',
        file: 'Chọn file...',
        checkbox: 'Chọn các tùy chọn...',
        radio: 'Chọn một tùy chọn...'
      };

      if (!$placeholderInput.val() && suggestions[fieldType]) {
        $placeholderInput.attr('placeholder', suggestions[fieldType]);
      }
    } catch (error) {
      console.error('Error updating placeholder suggestions:', error);
    }
  }

  function addFieldOption(button) {
    try {
      const $button = $(button);
      const $optionsContainer = $button.closest('.field-options-section').find('.options-container');

      if ($optionsContainer.length === 0) {
        console.warn('Options container not found');
        return;
      }

      const optionIndex = $optionsContainer.children().length;

      const optionHtml = `
        <div class="option-item row g-2 mb-2">
          <div class="col-md-4">
            <input type="text" name="options[${optionIndex}][key]" class="form-control" placeholder="Giá trị" required>
          </div>
          <div class="col-md-6">
            <input type="text" name="options[${optionIndex}][value]" class="form-control" placeholder="Nhãn hiển thị" required>
          </div>
          <div class="col-md-2">
            <button type="button" class="btn btn-outline-danger btn-sm remove-option-btn w-100">
              <i class="ri-delete-bin-line"></i>
            </button>
          </div>
        </div>
      `;

      $optionsContainer.append(optionHtml);

      // Focus on the first input of the new option
      const $newOption = $optionsContainer.children().last();
      $newOption.find('input').first().focus();

    } catch (error) {
      console.error('Error adding field option:', error);
      showToast('error', 'Có lỗi xảy ra khi thêm tùy chọn');
    }
  }

  function removeFieldOption(button) {
    try {
      // Ensure we're working with jQuery object
      const $button = $(button);
      const $optionItem = $button.closest('.option-item');
      const $container = $button.closest('.field-options-section').find('.options-container');

      if ($optionItem.length === 0) {
        console.warn('Option item not found');
        return;
      }

      // Remove the option item
      $optionItem.remove();

      // Reindex remaining options using jQuery
      if ($container.length > 0) {
        reindexOptionsJQuery($container);
      }
    } catch (error) {
      console.error('Error removing field option:', error);
      showToast('error', 'Có lỗi xảy ra khi xóa tùy chọn');
    }
  }


  function reindexOptions(container) {
    try {
      if (!container) {
        console.warn('Container not provided for reindexing');
        return;
      }

      const optionItems = container.querySelectorAll('.option-item');
      optionItems.forEach((item, index) => {
        const keyInput = item.querySelector('[name*="[key]"]');
        const valueInput = item.querySelector('[name*="[value]"]');

        if (keyInput) keyInput.name = `options[${index}][key]`;
        if (valueInput) valueInput.name = `options[${index}][value]`;
      });
    } catch (error) {
      console.error('Error reindexing options:', error);
    }
  }

  // jQuery version of reindexOptions for consistency
  function reindexOptionsJQuery($container) {
    try {
      if (!$container || $container.length === 0) {
        console.warn('Container not found for reindexing');
        return;
      }

      $container.find('.option-item').each(function(index) {
        const $item = $(this);
        const $keyInput = $item.find('[name*="[key]"]');
        const $valueInput = $item.find('[name*="[value]"]');

        if ($keyInput.length) $keyInput.attr('name', `options[${index}][key]`);
        if ($valueInput.length) $valueInput.attr('name', `options[${index}][value]`);
      });
    } catch (error) {
      console.error('Error reindexing options with jQuery:', error);
    }
  }

  // Generate field HTML for forms
  function generateFieldHTML(field, namePrefix = '', value = null) {
    const fieldName = namePrefix ? `${namePrefix}[${field.name}]` : field.name;
    const isRequired = field.is_required || (field.pivot && field.pivot.is_required);
    const requiredAttr = isRequired ? 'required' : '';
    const requiredLabel = isRequired ? ' *' : '';

    let html = '';

    switch (field.type) {
      case 'text':
        html = `
          <div class="form-floating form-floating-outline">
            <input type="text"
                   name="${fieldName}"
                   class="form-control"
                   ${requiredAttr}
                   placeholder="${field.placeholder || ''}"
                   value="${value || ''}">
            <label>${field.label}${requiredLabel}</label>
          </div>`;
        break;

      case 'number':
        html = `
          <div class="form-floating form-floating-outline">
            <input type="number"
                   name="${fieldName}"
                   class="form-control"
                   ${requiredAttr}
                   placeholder="${field.placeholder || ''}"
                   value="${value || ''}">
            <label>${field.label}${requiredLabel}</label>
          </div>`;
        break;

      case 'date':
        html = `
          <div class="form-floating form-floating-outline">
            <input type="date"
                   name="${fieldName}"
                   class="form-control"
                   ${requiredAttr}
                   value="${value || ''}">
            <label>${field.label}${requiredLabel}</label>
          </div>`;
        break;

      case 'select':
        html = `
          <div class="form-floating form-floating-outline">
            <select name="${fieldName}" class="form-select" ${requiredAttr}>
              <option value="">Chọn...</option>`;

        const options = field.custom_options || field.options || {};
        Object.keys(options).forEach(key => {
          const selected = value === key ? 'selected' : '';
          html += `<option value="${key}" ${selected}>${options[key]}</option>`;
        });

        html += `</select>
            <label>${field.label}${requiredLabel}</label>
          </div>`;
        break;

      case 'textarea':
        html = `
          <div class="form-floating form-floating-outline">
            <textarea name="${fieldName}"
                      class="form-control"
                      ${requiredAttr}
                      placeholder="${field.placeholder || ''}"
                      rows="3">${value || ''}</textarea>
            <label>${field.label}${requiredLabel}</label>
          </div>`;
        break;

      case 'file':
        html = `
          <div class="form-floating form-floating-outline">
            <input type="file"
                   name="${fieldName}"
                   class="form-control"
                   ${requiredAttr}>
            <label>${field.label}${requiredLabel}</label>
          </div>`;
        break;

      case 'checkbox':
        html = '<div class="form-check-group">';
        html += `<label class="form-label">${field.label}${requiredLabel}</label>`;

        const checkboxOptions = field.custom_options || field.options || {};
        const checkboxValues = Array.isArray(value) ? value : (value ? [value] : []);

        Object.keys(checkboxOptions).forEach(key => {
          const checked = checkboxValues.includes(key) ? 'checked' : '';
          html += `
            <div class="form-check">
              <input type="checkbox"
                     name="${fieldName}[]"
                     value="${key}"
                     class="form-check-input"
                     ${checked}
                     ${requiredAttr}>
              <label class="form-check-label">${checkboxOptions[key]}</label>
            </div>`;
        });

        html += '</div>';
        break;

      case 'radio':
        html = '<div class="form-check-group">';
        html += `<label class="form-label">${field.label}${requiredLabel}</label>`;

        const radioOptions = field.custom_options || field.options || {};
        Object.keys(radioOptions).forEach(key => {
          const checked = value === key ? 'checked' : '';
          html += `
            <div class="form-check">
              <input type="radio"
                     name="${fieldName}"
                     value="${key}"
                     class="form-check-input"
                     ${checked}
                     ${requiredAttr}>
              <label class="form-check-label">${radioOptions[key]}</label>
            </div>`;
        });

        html += '</div>';
        break;

      default:
        html = `
          <div class="form-floating form-floating-outline">
            <input type="text"
                   name="${fieldName}"
                   class="form-control"
                   ${requiredAttr}
                   placeholder="${field.placeholder || ''}"
                   value="${value || ''}">
            <label>${field.label}${requiredLabel}</label>
          </div>`;
    }

    // Add help text if available
    if (field.help_text) {
      html += `<small class="form-text text-muted">${field.help_text}</small>`;
    }

    return html;
  }

  // Validate field values
  function validateFieldValue(field, value) {
    const errors = [];

    // Check required
    if ((field.is_required || (field.pivot && field.pivot.is_required)) && !value) {
      errors.push(`${field.label} là bắt buộc`);
      return errors;
    }

    if (!value) return errors; // Skip other validations if empty and not required

    // Type-specific validations
    switch (field.type) {
      case 'number':
        if (isNaN(value)) {
          errors.push(`${field.label} phải là số`);
        }
        break;

      case 'date':
        if (!isValidDate(value)) {
          errors.push(`${field.label} phải là ngày hợp lệ`);
        }
        break;

      case 'select':
      case 'radio':
        const options = field.custom_options || field.options || {};
        if (!Object.keys(options).includes(value)) {
          errors.push(`${field.label} có giá trị không hợp lệ`);
        }
        break;

      case 'checkbox':
        if (!Array.isArray(value)) {
          errors.push(`${field.label} phải là mảng`);
        }
        break;
    }

    // Custom validation rules
    if (field.validation_rules && Array.isArray(field.validation_rules)) {
      field.validation_rules.forEach(rule => {
        const error = validateRule(rule, value, field.label);
        if (error) errors.push(error);
      });
    }

    return errors;
  }

  function validateRule(rule, value, fieldLabel) {
    if (typeof rule === 'string') {
      const [ruleName, ruleValue] = rule.split(':');

      switch (ruleName) {
        case 'min':
          if (value.length < parseInt(ruleValue)) {
            return `${fieldLabel} phải có ít nhất ${ruleValue} ký tự`;
          }
          break;

        case 'max':
          if (value.length > parseInt(ruleValue)) {
            return `${fieldLabel} không được vượt quá ${ruleValue} ký tự`;
          }
          break;

        case 'email':
          if (!isValidEmail(value)) {
            return `${fieldLabel} phải là email hợp lệ`;
          }
          break;

        case 'url':
          if (!isValidUrl(value)) {
            return `${fieldLabel} phải là URL hợp lệ`;
          }
          break;
      }
    }

    return null;
  }

  // Utility functions
  function isValidDate(dateString) {
    const date = new Date(dateString);
    return date instanceof Date && !isNaN(date);
  }

  function isValidEmail(email) {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
  }

  function isValidUrl(url) {
    try {
      new URL(url);
      return true;
    } catch {
      return false;
    }
  }

  // Simple toast function if not available globally
  function showToast(type, message) {
    if (typeof window.showToast === 'function') {
      window.showToast(type, message);
    } else {
      console.log(`${type.toUpperCase()}: ${message}`);
      // Fallback to alert for critical errors and warnings
      if (type === 'error') {
        alert('Lỗi: ' + message);
      } else if (type === 'warning') {
        console.warn('Cảnh báo: ' + message);
      }
    }
  }

  // Debug function to check field types
  function debugFieldTypes() {
    console.log('=== DYNAMIC FIELDS DEBUG INFO ===');
    console.log('Available field type configs:', Object.keys(fieldTypeConfigs));
    console.log('Full fieldTypeConfigs object:', fieldTypeConfigs);

    if (typeof window.fieldTypes !== 'undefined') {
      console.log('Backend field types from window.fieldTypes:', window.fieldTypes);
    } else {
      console.warn('window.fieldTypes not available');
    }

    // Check for any field elements in the DOM
    const fieldTypeSelects = document.querySelectorAll('[name="type"]');
    console.log('Found field type selects in DOM:', fieldTypeSelects.length);

    fieldTypeSelects.forEach((select, index) => {
      console.log(`Select ${index + 1} current value:`, select.value);
    });

    console.log('=== END DEBUG INFO ===');
  }

  // Export functions for global use
  window.DynamicFields = {
    generateFieldHTML,
    validateFieldValue,
    fieldTypeConfigs,
    initializeDynamicFields,
    handleFieldTypeChange,
    addFieldOption,
    removeFieldOption,
    debugFieldTypes
  };

  // Initialize when DOM is ready
  if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', initializeDynamicFields);
  } else {
    initializeDynamicFields();
  }

});
